/*
 * تصميم حديث مشابه لأنظمة Google Material Design
 * نظام إدارة سيارات الحمل
 */

/* استيراد خطوط Google */
@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/icon?family=Material+Icons");

/* المتغيرات الأساسية */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #dbeafe;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --success-color: #16a34a;
  --warning-color: #ea580c;
  --error-color: #dc2626;
  --info-color: #0ea5e9;

  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --on-surface: #475569;
  --on-primary: #ffffff;

  --border-radius: 8px;
  --border-radius-large: 12px;
  --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-2: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-3: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);

  --transition: all 0.2s ease-in-out;
}

/* إعدادات عامة */
* {
  box-sizing: border-box;
}

body {
  font-family: "Cairo", sans-serif;
  background-color: var(--background-color);
  color: var(--on-surface);
  line-height: 1.6;
  font-size: 14px;
}

/* تحسين الخطوط */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
  margin-bottom: 1rem;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Cairo", sans-serif;
}

/* القائمة الجانبية */
.sidebar {
  background: #ffffff;
  min-height: 100vh;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border-left: 1px solid #e5e7eb;
  position: fixed;
  top: 0;
  right: 0;
  width: 280px;
  z-index: 1000;
}

/* المحتوى الرئيسي */
.main-content {
  margin-right: 280px;
  min-height: 100vh;
  background-color: var(--background-color);
}

.sidebar .nav-link {
  color: #6b7280;
  padding: 12px 20px;
  margin: 4px 12px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 500;
  display: flex;
  align-items: center;
  text-decoration: none;
}

.sidebar .nav-link:hover {
  background-color: #f3f4f6;
  color: var(--primary-color);
  transform: translateX(-2px);
}

.sidebar .nav-link.active {
  background-color: #eff6ff;
  color: var(--primary-color);
  font-weight: 600;
  border-right: 3px solid var(--primary-color);
}

.sidebar .nav-link i {
  margin-left: 12px;
  font-size: 20px;
  width: 24px;
  text-align: center;
}

/* شعار النظام */
.sidebar .text-center h5 {
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.3rem;
  margin-bottom: 0;
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
}

/* البطاقات */
.card {
  background-color: var(--surface-color);
  border: none;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-1);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-2);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--surface-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 20px 24px;
  font-weight: 500;
}

.card-body {
  padding: 24px;
}

.card-title {
  font-weight: 500;
  color: var(--on-surface);
  margin-bottom: 8px;
}

/* الأزرار */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 10px 24px;
  transition: var(--transition);
  border: none;
  text-transform: none;
  font-family: "Cairo", sans-serif;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--on-primary);
  box-shadow: var(--shadow-1);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-2);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
  box-shadow: var(--shadow-1);
}

.btn-success:hover {
  background-color: #45a049;
  box-shadow: var(--shadow-2);
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
  box-shadow: var(--shadow-1);
}

.btn-warning:hover {
  background-color: #f57c00;
  box-shadow: var(--shadow-2);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
  box-shadow: var(--shadow-1);
}

.btn-danger:hover {
  background-color: #d32f2f;
  box-shadow: var(--shadow-2);
  transform: translateY(-1px);
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: var(--on-primary);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 6px 16px;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 12px 32px;
  font-size: 1.1rem;
}

/* النماذج */
.form-control {
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  padding: 12px 16px;
  transition: var(--transition);
  font-family: "Cairo", sans-serif;
  background-color: var(--surface-color);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  outline: none;
}

.form-label {
  font-weight: 500;
  color: var(--on-surface);
  margin-bottom: 8px;
}

.form-select {
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  padding: 12px 16px;
  transition: var(--transition);
  font-family: "Cairo", sans-serif;
  background-color: var(--surface-color);
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  outline: none;
}

/* الجداول */
.table {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-1);
}

.table thead th {
  background-color: #f5f5f5;
  border: none;
  font-weight: 600;
  color: var(--on-surface);
  padding: 16px;
  text-align: center;
}

.table tbody td {
  border: none;
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tbody tr:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* الشارات */
.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.75rem;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-danger {
  background-color: var(--error-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
}

.bg-info {
  background-color: var(--info-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

/* التنبيهات */
.alert {
  border: none;
  border-radius: var(--border-radius-large);
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-1);
}

.alert-success {
  background-color: #ecfdf5;
  color: #065f46;
  border-right: 4px solid var(--success-color);
}

.alert-danger {
  background-color: #fef2f2;
  color: #991b1b;
  border-right: 4px solid var(--error-color);
}

.alert-warning {
  background-color: #fffbeb;
  color: #92400e;
  border-right: 4px solid var(--warning-color);
}

.alert-info {
  background-color: #ecfeff;
  color: #155e75;
  border-right: 4px solid var(--info-color);
}

/* الإحصائيات */
.stats-card {
  background: #ffffff;
  border: 1px solid #f3f4f6;
  border-radius: var(--border-radius-large);
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
}

.stats-card.bg-success::before {
  background: var(--success-color);
}

.stats-card.bg-warning::before {
  background: var(--warning-color);
}

.stats-card.bg-info::before {
  background: var(--info-color);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stats-card .stats-icon {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.stats-card.bg-success .stats-icon {
  color: var(--success-color);
}

.stats-card.bg-warning .stats-icon {
  color: var(--warning-color);
}

.stats-card.bg-info .stats-icon {
  color: var(--info-color);
}

.stats-card .stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--on-surface);
}

.stats-card .stats-label {
  font-size: 1rem;
  color: var(--secondary-color);
  font-weight: 500;
}

/* شريط التنقل العلوي */
.top-navbar {
  background-color: var(--surface-color);
  box-shadow: var(--shadow-1);
  padding: 16px 0;
  margin-bottom: 24px;
  border-radius: var(--border-radius-large);
}

.top-navbar h1 {
  color: var(--on-surface);
  font-weight: 600;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: var(--secondary-color);
}

.user-info i {
  color: var(--primary-color);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    z-index: 1050;
    transition: var(--transition);
  }

  .sidebar.show {
    right: 0;
  }

  .main-content {
    margin-right: 0;
    padding: 0;
  }

  .top-navbar {
    margin: 0;
    border-radius: 0;
  }

  .content {
    padding: 16px;
  }

  .card-body {
    padding: 16px;
  }

  .btn {
    padding: 8px 16px;
    font-size: 0.875rem;
  }

  .stats-card .stats-number {
    font-size: 2rem;
  }

  .table-responsive {
    border-radius: var(--border-radius);
  }

  .user-info > div:not(:last-child) {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .sidebar {
    position: fixed;
    right: 0;
  }

  .main-content {
    margin-right: 280px;
  }
}

/* تحسينات إضافية */
.content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين المودال */
.modal-content {
  border: none;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-3);
}

.modal-header {
  background-color: var(--primary-color);
  color: var(--on-primary);
  border-bottom: none;
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 16px 24px;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-2);
  padding: 8px 0;
}

.dropdown-item {
  padding: 12px 20px;
  transition: var(--transition);
  font-family: "Cairo", sans-serif;
}

.dropdown-item:hover {
  background-color: rgba(25, 118, 210, 0.08);
  color: var(--primary-color);
}

/* تحسين شريط التقدم */
.progress {
  height: 8px;
  border-radius: 4px;
  background-color: #e0e0e0;
}

.progress-bar {
  border-radius: 4px;
  transition: var(--transition);
}

/* تحسين التبويبات */
.nav-tabs {
  border-bottom: 2px solid #e0e0e0;
}

.nav-tabs .nav-link {
  border: none;
  color: var(--secondary-color);
  font-weight: 500;
  padding: 12px 24px;
  transition: var(--transition);
}

.nav-tabs .nav-link.active {
  color: var(--primary-color);
  border-bottom: 3px solid var(--primary-color);
  background-color: transparent;
}

.nav-tabs .nav-link:hover {
  color: var(--primary-color);
  border-color: transparent;
}

/* تحسين البحث */
.search-box {
  position: relative;
}

.search-box .form-control {
  padding-right: 40px;
}

.search-box .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-color);
}

/* تحسين الأيقونات */
.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

/* تحسين الطباعة */
@media print {
  .sidebar,
  .btn,
  .no-print {
    display: none !important;
  }

  .main-content {
    margin: 0 !important;
    padding: 0 !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}

/* تحسينات للوضع المظلم (اختياري) */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #121212;
    --surface-color: #1e1e1e;
    --on-surface: #ffffff;
  }

  body {
    background-color: var(--background-color);
    color: var(--on-surface);
  }

  .card {
    background-color: var(--surface-color);
  }

  .form-control,
  .form-select {
    background-color: var(--surface-color);
    color: var(--on-surface);
    border-color: #424242;
  }

  .table {
    background-color: var(--surface-color);
    color: var(--on-surface);
  }

  .table thead th {
    background-color: #2c2c2c;
  }
}

/* تحسينات إضافية للتفاعل */
.clickable {
  cursor: pointer;
  transition: var(--transition);
}

.clickable:hover {
  transform: scale(1.02);
}

/* تحسين التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* تحسين التركيز */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: none;
}
