<?php
/**
 * الصفحة الرئيسية للنظام
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// بدء تخزين مؤقت للمخرجات
ob_start();

// التحقق من تسجيل دخول المستخدم
requireLogin();

// تحديد الصفحة المطلوبة
$page = isset($_GET['page']) ? sanitizeInput($_GET['page']) : 'dashboard';

// قائمة الصفحات المسموح بها
$allowedPages = [
    'dashboard' => 'لوحة التحكم',
    'company_settings' => 'إعدادات الشركة',
    'camps' => 'إدارة المواقع',
    'drivers' => 'إدارة السائقين',
    'operations' => 'العمليات',
    'reports' => 'التقارير',
    'users' => 'المستخدمين',
    'profile' => 'الملف الشخصي',
    'settings' => 'الإعدادات'
];

// التحقف من وجود الصفحة المطلوبة
if (!array_key_exists($page, $allowedPages)) {
    $page = 'dashboard';
}

// التحقف من الصلاحيات للصفحة المطلوبة
switch ($page) {
    case 'company_settings':
        requirePermission('manage_companies');
        break;
        
    case 'camps':
        requirePermission('manage_camps');
        break;
        
    case 'drivers':
        requirePermission('manage_drivers');
        break;
        
    case 'reports':
        requirePermission('view_reports');
        break;
        
    case 'users':
        requirePermission('manage_companies');
        break;
}

// الحصول على معلومات المستخدم الحالي
$userInfo = fetchRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);

// جلب معلومات الشركة
$companyInfo = fetchRow("SELECT * FROM companies WHERE id = 1");
$companyName = $companyInfo ? $companyInfo['name'] : 'النرجس';

// تحديد الكمب الحالي
$campId = isset($_SESSION['camp_id']) ? $_SESSION['camp_id'] : null;
$campName = null;

if ($campId) {
    $campInfo = fetchRow("SELECT * FROM camps WHERE id = ?", [$campId]);
    if ($campInfo) {
        $campName = $campInfo['name'];
    }
}

// عرض القالب الرئيسي
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - <?php echo $allowedPages[$page]; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white"><?php echo SITE_NAME; ?></h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="index.php?page=dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        
                        <?php if (hasPermission('manage_companies')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'company_settings' ? 'active' : ''; ?>" href="index.php?page=company_settings">
                                <i class="fas fa-building me-2"></i>
                                إعدادات الشركة
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('manage_camps')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'camps' ? 'active' : ''; ?>" href="index.php?page=camps">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                إدارة المواقع
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('manage_drivers')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'drivers' ? 'active' : ''; ?>" href="index.php?page=drivers">
                                <i class="fas fa-id-card me-2"></i>
                                إدارة السائقين
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'operations' ? 'active' : ''; ?>" href="index.php?page=operations">
                                <i class="fas fa-truck me-2"></i>
                                العمليات
                            </a>
                        </li>
                        
                        <?php if (hasPermission('view_reports')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'reports' ? 'active' : ''; ?>" href="index.php?page=reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('manage_users')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'users' ? 'active' : ''; ?>" href="index.php?page=users">
                                <i class="fas fa-users me-2"></i>
                                المستخدمين
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'profile' ? 'active' : ''; ?>" href="index.php?page=profile">
                                <i class="fas fa-user me-2"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="container-fluid py-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h1 class="h3 mb-0"><?php echo $allowedPages[$page]; ?></h1>
                        <div class="text-end">
                            <div class="d-flex align-items-center">
                                <?php if (!empty($companyName)): ?>
                                <div class="me-3 text-muted">
                                    <i class="fas fa-building me-1"></i> <?php echo $companyName; ?>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($campName)): ?>
                                <div class="me-3 text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i> <?php echo $campName; ?>
                                </div>
                                <?php endif; ?>
                                <div>
                                    <i class="fas fa-user me-1"></i> <?php echo $userInfo['name']; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- محتوى الصفحة -->
                <div class="content">
                    <?php include "modules/$page.php"; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
<?php
// إنهاء التخزين المؤقت وإرسال المخرجات
ob_end_flush();
?>