<?php

/**
 * الصفحة الرئيسية للنظام
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// بدء تخزين مؤقت للمخرجات
ob_start();

// التحقق من تسجيل دخول المستخدم
requireLogin();

// تحديد الصفحة المطلوبة
$page = isset($_GET['page']) ? sanitizeInput($_GET['page']) : 'dashboard';

// قائمة الصفحات المسموح بها
$allowedPages = [
    'dashboard' => 'لوحة التحكم',
    'company_settings' => 'إعدادات الشركة',
    'camps' => 'إدارة المواقع',
    'drivers' => 'إدارة السائقين',
    'operations' => 'العمليات',
    'reports' => 'التقارير',
    'users' => 'المستخدمين',
    'profile' => 'الملف الشخصي',
    'settings' => 'الإعدادات'
];

// التحقف من وجود الصفحة المطلوبة
if (!array_key_exists($page, $allowedPages)) {
    $page = 'dashboard';
}

// التحقف من الصلاحيات للصفحة المطلوبة
switch ($page) {
    case 'company_settings':
        requirePermission('manage_companies');
        break;

    case 'camps':
        requirePermission('manage_camps');
        break;

    case 'drivers':
        requirePermission('manage_drivers');
        break;

    case 'reports':
        requirePermission('view_reports');
        break;

    case 'users':
        requirePermission('manage_companies');
        break;
}

// الحصول على معلومات المستخدم الحالي
$userInfo = fetchRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);

// جلب معلومات الشركة
$companyInfo = fetchRow("SELECT * FROM companies WHERE id = 1");
$companyName = $companyInfo ? $companyInfo['name'] : 'النرجس';

// تحديد الكمب الحالي
$campId = isset($_SESSION['camp_id']) ? $_SESSION['camp_id'] : null;
$campName = null;

if ($campId) {
    $campInfo = fetchRow("SELECT * FROM camps WHERE id = ?", [$campId]);
    if ($campInfo) {
        $campName = $campInfo['name'];
    }
}

// عرض القالب الرئيسي
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - <?php echo $allowedPages[$page]; ?></title>

    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Material Icons من Google -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Font Awesome للأيقونات الإضافية -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- خطوط Google -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- ملف CSS المخصص -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Meta tags إضافية -->
    <meta name="description" content="نظام إدارة عمليات دخول وخروج سيارات الحمل - تصميم حديث وعملي">
    <meta name="theme-color" content="#1976d2">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>

<body>
    <!-- القائمة الجانبية -->
    <nav class="sidebar d-md-block">
        <div class="position-sticky pt-3">
            <!-- شعار النظام -->
            <div class="text-center mb-4">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="material-icons" style="font-size: 32px; color: white; margin-left: 8px;">local_shipping</i>
                    <h5 class="text-white mb-0"><?php echo SITE_NAME; ?></h5>
                </div>
                <small class="text-white-50">نظام إدارة حديث وعملي</small>
            </div>

            <!-- القائمة الرئيسية -->
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="index.php?page=dashboard">
                        <i class="material-icons">dashboard</i>
                        لوحة التحكم
                    </a>
                </li>

                <?php if (hasPermission('manage_companies')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'company_settings' ? 'active' : ''; ?>" href="index.php?page=company_settings">
                            <i class="material-icons">business</i>
                            إعدادات الشركة
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (hasPermission('manage_camps')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'camps' ? 'active' : ''; ?>" href="index.php?page=camps">
                            <i class="material-icons">location_on</i>
                            إدارة المواقع
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (hasPermission('manage_drivers')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'drivers' ? 'active' : ''; ?>" href="index.php?page=drivers">
                            <i class="material-icons">people</i>
                            إدارة السائقين
                        </a>
                    </li>
                <?php endif; ?>

                <li class="nav-item">
                    <a class="nav-link <?php echo $page === 'operations' ? 'active' : ''; ?>" href="index.php?page=operations">
                        <i class="material-icons">local_shipping</i>
                        العمليات
                    </a>
                </li>

                <?php if (hasPermission('view_reports')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'reports' ? 'active' : ''; ?>" href="index.php?page=reports">
                            <i class="material-icons">assessment</i>
                            التقارير
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (hasPermission('manage_users')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'users' ? 'active' : ''; ?>" href="index.php?page=users">
                            <i class="material-icons">admin_panel_settings</i>
                            المستخدمين
                        </a>
                    </li>
                <?php endif; ?>

                <!-- فاصل -->
                <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                <li class="nav-item">
                    <a class="nav-link <?php echo $page === 'profile' ? 'active' : ''; ?>" href="index.php?page=profile">
                        <i class="material-icons">account_circle</i>
                        الملف الشخصي
                    </a>
                </li>

                <li class="nav-item mt-3">
                    <a class="nav-link text-danger" href="logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                        <i class="material-icons">logout</i>
                        تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <!-- عنوان الصفحة -->
                    <div class="d-flex align-items-center">
                        <button class="btn btn-primary d-md-none me-3" onclick="toggleMobileSidebar()" type="button">
                            <i class="material-icons">menu</i>
                        </button>
                        <h1 class="h3 mb-0">
                            <i class="material-icons me-2" style="vertical-align: middle;">
                                <?php
                                $pageIcons = [
                                    'dashboard' => 'dashboard',
                                    'company_settings' => 'business',
                                    'camps' => 'location_on',
                                    'drivers' => 'people',
                                    'operations' => 'local_shipping',
                                    'reports' => 'assessment',
                                    'users' => 'admin_panel_settings',
                                    'profile' => 'account_circle',
                                    'settings' => 'settings'
                                ];
                                echo $pageIcons[$page] ?? 'dashboard';
                                ?>
                            </i>
                            <?php echo $allowedPages[$page]; ?>
                        </h1>
                    </div>

                    <!-- معلومات المستخدم والشركة -->
                    <div class="user-info">
                        <?php if (!empty($companyName)): ?>
                            <div class="d-none d-sm-flex align-items-center me-3">
                                <i class="material-icons me-1">business</i>
                                <span><?php echo $companyName; ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($campName)): ?>
                            <div class="d-none d-sm-flex align-items-center me-3">
                                <i class="material-icons me-1">location_on</i>
                                <span><?php echo $campName; ?></span>
                            </div>
                        <?php endif; ?>

                        <div class="d-flex align-items-center">
                            <i class="material-icons me-1">account_circle</i>
                            <span><?php echo $userInfo['name']; ?></span>
                        </div>

                        <!-- قائمة منسدلة للمستخدم -->
                        <div class="dropdown ms-2">
                            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="material-icons" style="font-size: 16px;">more_vert</i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="index.php?page=profile">
                                        <i class="material-icons me-2" style="font-size: 18px;">account_circle</i>
                                        الملف الشخصي
                                    </a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item text-danger" href="logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                                        <i class="material-icons me-2" style="font-size: 18px;">logout</i>
                                        تسجيل الخروج
                                    </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى الصفحة -->
        <div class="content">
            <div class="container-fluid">
                <?php include "modules/$page.php"; ?>
            </div>
        </div>
    </main>

    <!-- Bootstrap 5.3.2 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <!-- Chart.js للرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- ملف JavaScript المخصص -->
    <script src="assets/js/script.js"></script>
</body>

</html>
<?php
// إنهاء التخزين المؤقت وإرسال المخرجات
ob_end_flush();
?>