# نظام إدارة عمليات دخول وخروج سيارات الحمل في المواقع

## نظرة عامة

نظام ويب باللغة العربية لإدارة ومتابعة عمليات دخول وخروج سيارات الحمل في مواقع الشركات. يهدف النظام إلى تسهيل تسجيل العمليات، متابعة السائقين، وحساب التكاليف بدقة، مع دعم الصلاحيات المتعددة وهيكلية متعددة المواقع.

## ✨ التحديثات الجديدة

- **تصميم حديث**: تم تحديث التصميم ليصبح مشابهاً لأنظمة Google Material Design
- **Bootstrap 5.3.2**: استخدام أحدث إصدار من Bootstrap مع دعم RTL كامل
- **Material Icons**: استخدام أيقونات Google Material Icons الحديثة
- **خطوط Cairo**: استخدام خط Cairo الجميل من Google Fonts
- **تصميم متجاوب**: تحسينات كبيرة للعرض على الأجهزة المحمولة
- **تأثيرات بصرية**: إضافة تأثيرات انتقالية سلسة وحديثة
- **ألوان متناسقة**: نظام ألوان متناسق مع متغيرات CSS

## الخصائص الأساسية

- إدارة المواقع (دعم متعدد الشركات)
- إدارة السائقين مع توليد QR Code
- تسجيل عمليات الدخول والخروج
- نظام صلاحيات متعدد المستويات
- لوحة تحكم وتقارير شاملة
- تصميم حديث وعملي مشابه لأنظمة Google

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- مكتبات QR Code
- متصفح ويب حديث يدعم CSS Grid و Flexbox

## طريقة التثبيت

1. قم بتنزيل الملفات إلى مجلد الويب الخاص بك
2. قم بإنشاء قاعدة بيانات MySQL جديدة
3. قم بتعديل ملف `config/database.php` بمعلومات الاتصال بقاعدة البيانات
4. قم بتشغيل السكريبت `install.php` لإعداد قاعدة البيانات
5. قم بتسجيل الدخول باستخدام اسم المستخدم وكلمة المرور الافتراضية: admin / admin123

## هيكل المشروع

- `assets/` - ملفات CSS و JavaScript والصور
  - `css/style.css` - ملف CSS مخصص بتصميم Material Design
  - `js/script.js` - ملف JavaScript للتفاعلات المتقدمة
  - `images/` - الصور والأيقونات
- `config/` - ملفات الإعدادات
- `includes/` - الدوال والكلاسات المساعدة
- `modules/` - وحدات النظام المختلفة
- `templates/` - قوالب العرض
- `index.php` - نقطة الدخول الرئيسية للنظام

## 🎨 ميزات التصميم الجديد

- **نظام ألوان متناسق**: استخدام متغيرات CSS لسهولة التخصيص
- **تأثيرات انتقالية**: تأثيرات سلسة عند التنقل والتفاعل
- **قائمة جانبية ثابتة**: تصميم حديث للقائمة الجانبية مع تدرج لوني
- **شريط تنقل علوي**: شريط تنقل أنيق مع معلومات المستخدم
- **بطاقات حديثة**: تصميم البطاقات بظلال وحواف مدورة
- **أزرار تفاعلية**: أزرار بتأثيرات hover وتدرجات لونية
- **نماذج محسنة**: حقول إدخال بتصميم Material Design
- **جداول أنيقة**: جداول بتصميم نظيف وتأثيرات hover

## 📱 التوافق مع الأجهزة المحمولة

- تصميم متجاوب بالكامل
- قائمة جانبية قابلة للطي على الشاشات الصغيرة
- أزرار وحقول محسنة للمس
- إخفاء تلقائي للعناصر غير الضرورية على الجوال

## 🛠️ التقنيات المستخدمة

- **Bootstrap 5.3.2 RTL**: إطار عمل CSS حديث مع دعم كامل للغة العربية
- **Material Icons**: أيقونات Google Material Design
- **Cairo Font**: خط عربي جميل من Google Fonts
- **CSS Custom Properties**: متغيرات CSS للتخصيص السهل
- **JavaScript ES6+**: كود JavaScript حديث ومحسن
- **Chart.js**: مكتبة الرسوم البيانية التفاعلية

## المطورون

تم تطوير هذا النظام لصالح شركة النرجس مع تحديثات التصميم الحديثة
