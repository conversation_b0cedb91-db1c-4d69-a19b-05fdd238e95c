# نظام إدارة عمليات دخول وخروج سيارات الحمل في المواقع

## نظرة عامة
نظام ويب باللغة العربية لإدارة ومتابعة عمليات دخول وخروج سيارات الحمل في مواقع الشركات. يهدف النظام إلى تسهيل تسجيل العمليات، متابعة السائقين، وحساب التكاليف بدقة، مع دعم الصلاحيات المتعددة وهيكلية متعددة المواقع.

## الخصائص الأساسية
- إدارة المواقع (دعم متعدد الشركات)
- إدارة السائقين مع توليد QR Code
- تسجيل عمليات الدخول والخروج
- نظام صلاحيات متعدد المستويات
- لوحة تحكم وتقارير شاملة

## متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- مكتبات QR Code
- متصفح ويب حديث

## طريقة التثبيت
1. قم بتنزيل الملفات إلى مجلد الويب الخاص بك
2. قم بإنشاء قاعدة بيانات MySQL جديدة
3. قم بتعديل ملف `config/database.php` بمعلومات الاتصال بقاعدة البيانات
4. قم بتشغيل السكريبت `install.php` لإعداد قاعدة البيانات
5. قم بتسجيل الدخول باستخدام اسم المستخدم وكلمة المرور الافتراضية: admin / admin123

## هيكل المشروع
- `assets/` - ملفات CSS و JavaScript والصور
- `config/` - ملفات الإعدادات
- `includes/` - الدوال والكلاسات المساعدة
- `modules/` - وحدات النظام المختلفة
- `templates/` - قوالب العرض
- `index.php` - نقطة الدخول الرئيسية للنظام

## المطورون
تم تطوير هذا النظام لصالح شركة النرجس