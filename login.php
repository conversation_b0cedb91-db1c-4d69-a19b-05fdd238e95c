<?php

/**
 * صفحة تسجيل الدخول
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// إذا كان المستخدم مسجل الدخول بالفعل، يتم توجيهه إلى الصفحة الرئيسية
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$error = '';

// معالجة نموذج تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // التحقق من بيانات المستخدم
        $sql = "SELECT * FROM users WHERE username = ?";
        $user = fetchRow($sql, [$username]);

        if ($user && password_verify($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['camp_id'] = $user['camp_id'];

            // تسجيل عملية تسجيل الدخول
            $logData = [
                'user_id' => $user['id'],
                'action' => 'login',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'timestamp' => date('Y-m-d H:i:s')
            ];
            insertData('activity_logs', $logData);

            header('Location: index.php');
            exit;
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Material Icons من Google -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Font Awesome للأيقونات الإضافية -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- خطوط Google -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- ملف CSS المخصص -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
            margin: 20px;
            padding: 40px;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-logo {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-logo .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 16px rgba(25, 118, 210, 0.3);
        }

        .login-logo .logo-icon i {
            font-size: 40px;
            color: white;
        }

        .login-logo h3 {
            color: #1976d2;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .login-logo p {
            color: #666;
            font-size: 14px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-floating .form-control {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px 16px 8px;
            height: auto;
            font-size: 16px;
        }

        .form-floating .form-control:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        .form-floating label {
            color: #666;
            font-weight: 500;
        }

        .btn-login {
            width: 100%;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            border: none;
            color: white;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(25, 118, 210, 0.3);
            background: linear-gradient(135deg, #1565c0, #1976d2);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px;
            margin-bottom: 24px;
        }

        .alert-danger {
            background-color: #ffebee;
            color: #c62828;
            border-right: 4px solid #f44336;
        }

        .footer-text {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }

        .input-group-text {
            background-color: transparent;
            border: 2px solid #e0e0e0;
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .form-control.password-input {
            border-left: none;
            border-radius: 12px 0 0 12px;
        }

        .password-toggle {
            cursor: pointer;
            color: #666;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #1976d2;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-container">
                    <div class="login-logo">
                        <div class="logo-icon">
                            <i class="material-icons">local_shipping</i>
                        </div>
                        <h3><?php echo SITE_NAME; ?></h3>
                        <p>نظام إدارة عمليات دخول وخروج سيارات الحمل</p>
                    </div>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <i class="material-icons me-2" style="vertical-align: middle;">error</i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="" id="loginForm">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                            <label for="username">
                                <i class="material-icons me-2" style="font-size: 18px; vertical-align: middle;">person</i>
                                اسم المستخدم
                            </label>
                        </div>

                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                            <label for="password">
                                <i class="material-icons me-2" style="font-size: 18px; vertical-align: middle;">lock</i>
                                كلمة المرور
                            </label>
                        </div>

                        <button type="submit" class="btn btn-login">
                            <i class="material-icons me-2" style="font-size: 18px; vertical-align: middle;">login</i>
                            تسجيل الدخول
                        </button>
                    </form>

                    <div class="footer-text">
                        <div class="mb-2">
                            <strong>بيانات الدخول الافتراضية:</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>المستخدم: <code>admin</code></span>
                            <span>كلمة المرور: <code>admin123</code></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>