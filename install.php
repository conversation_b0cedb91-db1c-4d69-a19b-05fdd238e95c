<?php
/**
 * ملف تثبيت النظام وإعداد قاعدة البيانات
 */

// تضمين ملف إعدادات قاعدة البيانات
require_once 'config/database.php';

$success = true;
$messages = [];

// إنشاء اتصال بقاعدة البيانات
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS);
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء قاعدة البيانات بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء قاعدة البيانات: " . $conn->error);
    }
    
    // اختيار قاعدة البيانات
    $conn->select_db(DB_NAME);
    
    // إنشاء جدول الشركات
    $sql = "CREATE TABLE IF NOT EXISTS companies (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(20),
        email VARCHAR(255),
        logo VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول الشركات بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول الشركات: " . $conn->error);
    }
    
    // إنشاء جدول المواقع
    $sql = "CREATE TABLE IF NOT EXISTS camps (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        company_id INT(11) NOT NULL,
        name VARCHAR(255) NOT NULL,
        location TEXT,
        manager_name VARCHAR(255),
        phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول المواقع بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول المواقع: " . $conn->error);
    }
    
    // إنشاء جدول المستخدمين
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(20),
        role ENUM('admin', 'camp_manager', 'accountant', 'worker') NOT NULL,
        company_id INT(11),
        camp_id INT(11),
        last_login DATETIME,
        status TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL,
        FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول المستخدمين بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول المستخدمين: " . $conn->error);
    }
    
    // إنشاء جدول السائقين
    $sql = "CREATE TABLE IF NOT EXISTS drivers (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        vehicle_type VARCHAR(100),
        vehicle_number VARCHAR(50),
        qr_code VARCHAR(255),
        notes TEXT,
        camp_id INT(11),
        created_by INT(11),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول السائقين بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول السائقين: " . $conn->error);
    }
    
    // إنشاء جدول المواد
    $sql = "CREATE TABLE IF NOT EXISTS materials (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        unit VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول المواد بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول المواد: " . $conn->error);
    }
    
    // إنشاء جدول العمليات
    $sql = "CREATE TABLE IF NOT EXISTS operations (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        driver_id INT(11) NOT NULL,
        material_id INT(11) NOT NULL,
        operation_type ENUM('entry', 'exit') NOT NULL,
        quantity DECIMAL(10,2) DEFAULT 1,
        cost DECIMAL(10,2),
        camp_id INT(11) NOT NULL,
        created_by INT(11) NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE,
        FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
        FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول العمليات بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول العمليات: " . $conn->error);
    }
    
    // إنشاء جدول سجلات النشاط
    $sql = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11),
        action VARCHAR(255) NOT NULL,
        details TEXT,
        ip_address VARCHAR(50),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول سجلات النشاط بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول سجلات النشاط: " . $conn->error);
    }
    
    // إضافة شركة افتراضية
    $sql = "INSERT INTO companies (name, address, phone, email) 
            SELECT 'شركة النرجس', 'العراق', '07700000000', '<EMAIL>'
            FROM dual
            WHERE NOT EXISTS (SELECT 1 FROM companies LIMIT 1)";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة الشركة الافتراضية بنجاح";
    } else {
        throw new Exception("خطأ في إضافة الشركة الافتراضية: " . $conn->error);
    }
    
    // إضافة كمب افتراضي
    $sql = "INSERT INTO camps (company_id, name, location, manager_name, phone) 
            SELECT 1, 'الكمب الرئيسي', 'العراق', 'مدير الكمب', '07700000000'
            FROM dual
            WHERE NOT EXISTS (SELECT 1 FROM camps LIMIT 1)";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة الكمب الافتراضي بنجاح";
    } else {
        throw new Exception("خطأ في إضافة الكمب الافتراضي: " . $conn->error);
    }
    
    // إضافة مستخدم مدير النظام الافتراضي
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (name, username, password, email, role, company_id) 
            SELECT 'مدير النظام', 'admin', '$adminPassword', '<EMAIL>', 'admin', 1
            FROM dual
            WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin')";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة مستخدم مدير النظام الافتراضي بنجاح";
    } else {
        throw new Exception("خطأ في إضافة مستخدم مدير النظام: " . $conn->error);
    }
    
    // إضافة بعض المواد الافتراضية
    $sql = "INSERT INTO materials (name, price, unit) VALUES 
            ('رمل', 11000, 'متر مكعب'),
            ('حصو', 12000, 'متر مكعب'),
            ('بلوك', 10000, 'متر مكعب')
            ON DUPLICATE KEY UPDATE name = VALUES(name)";
    
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة المواد الافتراضية بنجاح";
    } else {
        throw new Exception("خطأ في إضافة المواد الافتراضية: " . $conn->error);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    $success = false;
    $messages[] = $e->getMessage();
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت النظام - <?php echo SITE_NAME ?? 'نظام إدارة سيارات الحمل'; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .install-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .install-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .install-header i {
            font-size: 50px;
            color: #0d6efd;
            margin-bottom: 15px;
        }
        .install-steps {
            margin-bottom: 30px;
        }
        .install-step {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .install-step.success {
            background-color: #d1e7dd;
        }
        .install-step.error {
            background-color: #f8d7da;
        }
        .install-footer {
            margin-top: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <i class="fas fa-truck-moving"></i>
                <h2>تثبيت نظام إدارة سيارات الحمل</h2>
                <p class="text-muted">إعداد قاعدة البيانات وتهيئة النظام</p>
            </div>
            
            <div class="install-steps">
                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    تم تثبيت النظام بنجاح!
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ أثناء تثبيت النظام.
                </div>
                <?php endif; ?>
                
                <?php foreach ($messages as $message): ?>
                <div class="install-step <?php echo $success ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $success ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                    <?php echo $message; ?>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="install-footer">
                <?php if ($success): ?>
                <p>يمكنك الآن تسجيل الدخول باستخدام:</p>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
                <div class="mt-4">
                    <a href="login.php" class="btn btn-primary">انتقل إلى صفحة تسجيل الدخول</a>
                </div>
                <?php else: ?>
                <div class="mt-4">
                    <a href="install.php" class="btn btn-danger">إعادة المحاولة</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>