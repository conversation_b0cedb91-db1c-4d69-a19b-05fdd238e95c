<?php

/**
 * ملف تثبيت النظام وإعداد قاعدة البيانات
 */

// تضمين ملف إعدادات قاعدة البيانات
require_once 'config/database.php';

$success = true;
$messages = [];

// إنشاء اتصال بقاعدة البيانات
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS);

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء قاعدة البيانات بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء قاعدة البيانات: " . $conn->error);
    }

    // اختيار قاعدة البيانات
    $conn->select_db(DB_NAME);

    // إنشاء جدول الشركات
    $sql = "CREATE TABLE IF NOT EXISTS companies (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(20),
        email VARCHAR(255),
        logo VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول الشركات بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول الشركات: " . $conn->error);
    }

    // إنشاء جدول المواقع
    $sql = "CREATE TABLE IF NOT EXISTS camps (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        company_id INT(11) NOT NULL,
        name VARCHAR(255) NOT NULL,
        location TEXT,
        manager_name VARCHAR(255),
        phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول المواقع بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول المواقع: " . $conn->error);
    }

    // إنشاء جدول المستخدمين
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(20),
        role ENUM('admin', 'camp_manager', 'accountant', 'worker') NOT NULL,
        company_id INT(11),
        camp_id INT(11),
        last_login DATETIME,
        status TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL,
        FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول المستخدمين بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول المستخدمين: " . $conn->error);
    }

    // إنشاء جدول السائقين
    $sql = "CREATE TABLE IF NOT EXISTS drivers (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        vehicle_type VARCHAR(100),
        vehicle_number VARCHAR(50),
        qr_code VARCHAR(255),
        notes TEXT,
        camp_id INT(11),
        created_by INT(11),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول السائقين بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول السائقين: " . $conn->error);
    }

    // إنشاء جدول المواد
    $sql = "CREATE TABLE IF NOT EXISTS materials (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        unit VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول المواد بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول المواد: " . $conn->error);
    }

    // إنشاء جدول العمليات
    $sql = "CREATE TABLE IF NOT EXISTS operations (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        driver_id INT(11) NOT NULL,
        material_id INT(11) NOT NULL,
        operation_type ENUM('entry', 'exit') NOT NULL,
        quantity DECIMAL(10,2) DEFAULT 1,
        cost DECIMAL(10,2),
        camp_id INT(11) NOT NULL,
        created_by INT(11) NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE,
        FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
        FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول العمليات بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول العمليات: " . $conn->error);
    }

    // إنشاء جدول سجلات النشاط
    $sql = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11),
        action VARCHAR(255) NOT NULL,
        details TEXT,
        ip_address VARCHAR(50),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إنشاء جدول سجلات النشاط بنجاح";
    } else {
        throw new Exception("خطأ في إنشاء جدول سجلات النشاط: " . $conn->error);
    }

    // إضافة شركة افتراضية
    $sql = "INSERT INTO companies (name, address, phone, email)
            SELECT 'شركة النرجس', 'العراق', '07700000000', '<EMAIL>'
            FROM dual
            WHERE NOT EXISTS (SELECT 1 FROM companies LIMIT 1)";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة الشركة الافتراضية بنجاح";
    } else {
        throw new Exception("خطأ في إضافة الشركة الافتراضية: " . $conn->error);
    }

    // إضافة كمب افتراضي
    $sql = "INSERT INTO camps (company_id, name, location, manager_name, phone)
            SELECT 1, 'الكمب الرئيسي', 'العراق', 'مدير الكمب', '07700000000'
            FROM dual
            WHERE NOT EXISTS (SELECT 1 FROM camps LIMIT 1)";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة الكمب الافتراضي بنجاح";
    } else {
        throw new Exception("خطأ في إضافة الكمب الافتراضي: " . $conn->error);
    }

    // إضافة مستخدم مدير النظام الافتراضي
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (name, username, password, email, role, company_id)
            SELECT 'مدير النظام', 'admin', '$adminPassword', '<EMAIL>', 'admin', 1
            FROM dual
            WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin')";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة مستخدم مدير النظام الافتراضي بنجاح";
    } else {
        throw new Exception("خطأ في إضافة مستخدم مدير النظام: " . $conn->error);
    }

    // إضافة بعض المواد الافتراضية
    $sql = "INSERT INTO materials (name, price, unit) VALUES
            ('رمل', 11000, 'متر مكعب'),
            ('حصو', 12000, 'متر مكعب'),
            ('بلوك', 10000, 'متر مكعب')
            ON DUPLICATE KEY UPDATE name = VALUES(name)";

    if ($conn->query($sql) === TRUE) {
        $messages[] = "تم إضافة المواد الافتراضية بنجاح";
    } else {
        throw new Exception("خطأ في إضافة المواد الافتراضية: " . $conn->error);
    }

    $conn->close();
} catch (Exception $e) {
    $success = false;
    $messages[] = $e->getMessage();
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت النظام - <?php echo SITE_NAME ?? 'نظام إدارة سيارات الحمل'; ?></title>
    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Material Icons من Google -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Font Awesome للأيقونات الإضافية -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- خطوط Google -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 50px 0;
            font-family: 'Cairo', sans-serif;
        }

        .install-container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .install-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
        }

        .install-header .logo-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 16px rgba(25, 118, 210, 0.3);
        }

        .install-header .logo-icon i {
            font-size: 50px;
            color: white;
        }

        .install-header h2 {
            color: #1976d2;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .install-header p {
            color: #666;
            font-size: 16px;
        }

        .install-steps {
            margin-bottom: 40px;
        }

        .install-step {
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .install-step.success {
            background-color: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .install-step.error {
            background-color: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        .install-step.info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1565c0;
        }

        .install-step i {
            font-size: 24px;
            margin-left: 15px;
            min-width: 30px;
        }

        .install-footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 30px;
            border-top: 2px solid #e0e0e0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(25, 118, 210, 0.3);
            background: linear-gradient(135deg, #1565c0, #1976d2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #e57373);
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(244, 67, 54, 0.3);
            background: linear-gradient(135deg, #d32f2f, #f44336);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 20px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
        }

        .alert-success {
            background-color: #e8f5e8;
            color: #2e7d32;
            border-right: 4px solid #4caf50;
        }

        .alert-danger {
            background-color: #ffebee;
            color: #c62828;
            border-right: 4px solid #f44336;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <div class="logo-icon">
                    <i class="material-icons">local_shipping</i>
                </div>
                <h2>تثبيت نظام إدارة سيارات الحمل</h2>
                <p>إعداد قاعدة البيانات وتهيئة النظام</p>
            </div>

            <div class="install-steps">
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="material-icons me-2">check_circle</i>
                        تم تثبيت النظام بنجاح!
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="material-icons me-2">error</i>
                        حدث خطأ أثناء تثبيت النظام.
                    </div>
                <?php endif; ?>

                <?php foreach ($messages as $message): ?>
                    <div class="install-step <?php echo $success ? 'success' : 'error'; ?>">
                        <i class="material-icons"><?php echo $success ? 'check' : 'close'; ?></i>
                        <?php echo $message; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="install-footer">
                <?php if ($success): ?>
                    <p>يمكنك الآن تسجيل الدخول باستخدام:</p>
                    <p><strong>اسم المستخدم:</strong> admin</p>
                    <p><strong>كلمة المرور:</strong> admin123</p>
                    <div class="mt-4">
                        <a href="login.php" class="btn btn-primary">انتقل إلى صفحة تسجيل الدخول</a>
                    </div>
                <?php else: ?>
                    <div class="mt-4">
                        <a href="install.php" class="btn btn-danger">إعادة المحاولة</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>

</html>