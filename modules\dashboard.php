<?php

/**
 * صفحة لوحة التحكم
 */

// الحصول على إحصائيات النظام
$stats = [];

// إحصائيات المواقع
$campQuery = "SELECT COUNT(*) as count FROM camps";
if (!hasPermission('manage_companies')) {
    $campQuery .= " WHERE id = ?";
    $stats['camps'] = fetchRow($campQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['camps'] = fetchRow($campQuery)['count'];
}

// إحصائيات السائقين
$driverQuery = "SELECT COUNT(*) as count FROM drivers";
if (!hasPermission('manage_companies')) {
    $driverQuery .= " WHERE camp_id = ?";
    $stats['drivers'] = fetchRow($driverQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['drivers'] = fetchRow($driverQuery)['count'];
}

// إحصائيات العمليات
$operationQuery = "SELECT COUNT(*) as count FROM operations";
if (!hasPermission('manage_companies')) {
    $operationQuery .= " WHERE camp_id = ?";
    $stats['operations'] = fetchRow($operationQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['operations'] = fetchRow($operationQuery)['count'];
}

// إحصائيات العمليات اليوم
$todayOperationQuery = "SELECT COUNT(*) as count FROM operations WHERE DATE(created_at) = CURDATE()";
if (!hasPermission('manage_companies')) {
    $todayOperationQuery .= " AND camp_id = ?";
    $stats['today_operations'] = fetchRow($todayOperationQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['today_operations'] = fetchRow($todayOperationQuery)['count'];
}

// إجمالي التكاليف
$costQuery = "SELECT SUM(cost) as total FROM operations";
if (!hasPermission('manage_companies')) {
    $costQuery .= " WHERE camp_id = ?";
    $totalCost = fetchRow($costQuery, [$_SESSION['camp_id']])['total'];
} else {
    $totalCost = fetchRow($costQuery)['total'];
}
$stats['total_cost'] = $totalCost ?: 0;

// الحصول على آخر العمليات
$recentOperationsQuery = "SELECT o.*, d.name as driver_name, m.name as material_name
                          FROM operations o
                          JOIN drivers d ON o.driver_id = d.id
                          JOIN materials m ON o.material_id = m.id";

if (!hasPermission('manage_companies')) {
    $recentOperationsQuery .= " WHERE o.camp_id = ?";
    $recentOperationsQuery .= " ORDER BY o.created_at DESC LIMIT 10";
    $recentOperations = fetchAll($recentOperationsQuery, [$_SESSION['camp_id']]);
} else {
    $recentOperationsQuery .= " ORDER BY o.created_at DESC LIMIT 10";
    $recentOperations = fetchAll($recentOperationsQuery);
}





// الحصول على بيانات الرسم البياني للمواد
$materialsQuery = "SELECT m.name, COUNT(o.id) as count
                  FROM materials m
                  LEFT JOIN operations o ON m.id = o.material_id";

if (!hasPermission('manage_companies')) {
    $materialsQuery .= " AND o.camp_id = ?";
    $materialsQuery .= " GROUP BY m.id ORDER BY count DESC";
    $materialsData = fetchAll($materialsQuery, [$_SESSION['camp_id']]);
} else {
    $materialsQuery .= " GROUP BY m.id ORDER BY count DESC";
    $materialsData = fetchAll($materialsQuery);
}

$materialsLabels = [];
$materialsData = [];

foreach ($materialsData as $material) {
    $materialsLabels[] = $material['name'];
    $materialsData[] = $material['count'];
}

$materialsDataJson = json_encode([
    'labels' => $materialsLabels,
    'data' => $materialsData
]);
?>

<div class="dashboard">
    <!-- معلومات الشركة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title"><i class="fas fa-building me-2"></i><?php echo $companyName; ?></h5>
                            <p class="card-text text-muted">مرحباً بك في نظام إدارة سيارات الحمل</p>
                        </div>
                        <a href="index.php?page=company_settings" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog me-1"></i> تعديل معلومات الشركة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي العمليات</h6>
                            <h2 class="card-text"><?php echo number_format($stats['operations']); ?></h2>
                        </div>
                        <i class="fas fa-truck-moving fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">عمليات اليوم</h6>
                            <h2 class="card-text"><?php echo number_format($stats['today_operations']); ?></h2>
                        </div>
                        <i class="fas fa-calendar-day fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">عدد السائقين</h6>
                            <h2 class="card-text"><?php echo number_format($stats['drivers']); ?></h2>
                        </div>
                        <i class="fas fa-id-card fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي التكاليف</h6>
                            <h2 class="card-text"><?php echo number_format($stats['total_cost']); ?></h2>
                        </div>
                        <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title">العمليات خلال الأسبوع الماضي</h5>
                </div>
                <div class="card-body">
                    <canvas id="operationsChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title">توزيع المواد</h5>
                </div>
                <div class="card-body">
                    <canvas id="materialsChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر العمليات -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title">آخر العمليات</h5>
                    <a href="index.php?page=operations" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>السائق</th>
                                    <th>المادة</th>
                                    <th>نوع العملية</th>
                                    <th>التكلفة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentOperations)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد عمليات حتى الآن</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recentOperations as $operation): ?>
                                        <tr>
                                            <td><?php echo $operation['id']; ?></td>
                                            <td><?php echo htmlspecialchars($operation['driver_name']); ?></td>
                                            <td><?php echo htmlspecialchars($operation['material_name']); ?></td>
                                            <td>
                                                <?php if ($operation['operation_type'] === 'entry'): ?>
                                                    <span class="badge bg-success">دخول</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">خروج</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo number_format($operation['cost']); ?> د.ع</td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($operation['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>