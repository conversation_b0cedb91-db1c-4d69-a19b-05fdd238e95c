<?php

/**
 * صفحة لوحة التحكم
 */

// الحصول على إحصائيات النظام
$stats = [];

// إحصائيات المواقع
$campQuery = "SELECT COUNT(*) as count FROM camps";
if (!hasPermission('manage_companies')) {
    $campQuery .= " WHERE id = ?";
    $stats['camps'] = fetchRow($campQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['camps'] = fetchRow($campQuery)['count'];
}

// إحصائيات السائقين
$driverQuery = "SELECT COUNT(*) as count FROM drivers";
if (!hasPermission('manage_companies')) {
    $driverQuery .= " WHERE camp_id = ?";
    $stats['drivers'] = fetchRow($driverQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['drivers'] = fetchRow($driverQuery)['count'];
}

// إحصائيات العمليات
$operationQuery = "SELECT COUNT(*) as count FROM operations";
if (!hasPermission('manage_companies')) {
    $operationQuery .= " WHERE camp_id = ?";
    $stats['operations'] = fetchRow($operationQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['operations'] = fetchRow($operationQuery)['count'];
}

// إحصائيات العمليات اليوم
$todayOperationQuery = "SELECT COUNT(*) as count FROM operations WHERE DATE(created_at) = CURDATE()";
if (!hasPermission('manage_companies')) {
    $todayOperationQuery .= " AND camp_id = ?";
    $stats['today_operations'] = fetchRow($todayOperationQuery, [$_SESSION['camp_id']])['count'];
} else {
    $stats['today_operations'] = fetchRow($todayOperationQuery)['count'];
}

// إجمالي التكاليف
$costQuery = "SELECT SUM(cost) as total FROM operations";
if (!hasPermission('manage_companies')) {
    $costQuery .= " WHERE camp_id = ?";
    $totalCost = fetchRow($costQuery, [$_SESSION['camp_id']])['total'];
} else {
    $totalCost = fetchRow($costQuery)['total'];
}
$stats['total_cost'] = $totalCost ?: 0;

// الحصول على آخر العمليات
$recentOperationsQuery = "SELECT o.*, d.name as driver_name, m.name as material_name
                          FROM operations o
                          JOIN drivers d ON o.driver_id = d.id
                          JOIN materials m ON o.material_id = m.id";

if (!hasPermission('manage_companies')) {
    $recentOperationsQuery .= " WHERE o.camp_id = ?";
    $recentOperationsQuery .= " ORDER BY o.created_at DESC LIMIT 10";
    $recentOperations = fetchAll($recentOperationsQuery, [$_SESSION['camp_id']]);
} else {
    $recentOperationsQuery .= " ORDER BY o.created_at DESC LIMIT 10";
    $recentOperations = fetchAll($recentOperationsQuery);
}

// الحصول على بيانات الرسم البياني للعمليات اليومية (آخر 7 أيام)
$chartDaysQuery = "SELECT DATE(created_at) as date,
                   SUM(CASE WHEN operation_type = 'entry' THEN 1 ELSE 0 END) as entry_count,
                   SUM(CASE WHEN operation_type = 'exit' THEN 1 ELSE 0 END) as exit_count
                   FROM operations
                   WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)";

if (!hasPermission('manage_companies')) {
    $chartDaysQuery .= " AND camp_id = ?";
    $chartDaysQuery .= " GROUP BY DATE(created_at) ORDER BY date";
    $chartData = fetchAll($chartDaysQuery, [$_SESSION['camp_id']]);
} else {
    $chartDaysQuery .= " GROUP BY DATE(created_at) ORDER BY date";
    $chartData = fetchAll($chartDaysQuery);
}

// تحويل بيانات الرسم البياني إلى تنسيق JSON
$chartLabels = [];
$chartEntryData = [];
$chartExitData = [];

// إنشاء مصفوفة للأيام السبعة الماضية
$days = [];
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $days[$date] = [
        'entry_count' => 0,
        'exit_count' => 0
    ];
}

// ملء البيانات الموجودة
foreach ($chartData as $data) {
    $days[$data['date']] = [
        'entry_count' => $data['entry_count'],
        'exit_count' => $data['exit_count']
    ];
}

// تحويل البيانات إلى تنسيق الرسم البياني
foreach ($days as $date => $counts) {
    $chartLabels[] = formatDate($date);
    $chartEntryData[] = $counts['entry_count'];
    $chartExitData[] = $counts['exit_count'];
}

$chartDataJson = json_encode([
    'labels' => $chartLabels,
    'entry' => $chartEntryData,
    'exit' => $chartExitData
]);

// الحصول على بيانات الرسم البياني للمواد
$materialsQuery = "SELECT m.name, COUNT(o.id) as count
                  FROM materials m
                  LEFT JOIN operations o ON m.id = o.material_id";

if (!hasPermission('manage_companies')) {
    $materialsQuery .= " AND o.camp_id = ?";
    $materialsQuery .= " GROUP BY m.id ORDER BY count DESC";
    $materialsData = fetchAll($materialsQuery, [$_SESSION['camp_id']]);
} else {
    $materialsQuery .= " GROUP BY m.id ORDER BY count DESC";
    $materialsData = fetchAll($materialsQuery);
}

$materialsLabels = [];
$materialsData = [];

foreach ($materialsData as $material) {
    $materialsLabels[] = $material['name'];
    $materialsData[] = $material['count'];
}

$materialsDataJson = json_encode([
    'labels' => $materialsLabels,
    'data' => $materialsData
]);

// الحصول على قائمة المواقع مع عدد العمليات والتكلفة الإجمالية
$campsQuery = "SELECT c.*, comp.name as company_name,
               COUNT(o.id) as operations_count,
               COALESCE(SUM(o.cost), 0) as total_cost,
               COUNT(CASE WHEN o.operation_type = 'entry' THEN 1 END) as entry_count,
               COUNT(CASE WHEN o.operation_type = 'exit' THEN 1 END) as exit_count
               FROM camps c
               LEFT JOIN companies comp ON c.company_id = comp.id
               LEFT JOIN operations o ON c.id = o.camp_id";

if (!hasPermission('manage_companies')) {
    $campsQuery .= " WHERE c.id = ?";
    $campsQuery .= " GROUP BY c.id ORDER BY c.name";
    $camps = fetchAll($campsQuery, [$_SESSION['camp_id']]);
} else {
    $campsQuery .= " GROUP BY c.id ORDER BY c.name";
    $camps = fetchAll($campsQuery);
}
?>

<div class="dashboard">
    <!-- معلومات الشركة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title"><i class="fas fa-building me-2"></i><?php echo $companyName; ?></h5>
                            <p class="card-text text-muted">مرحباً بك في نظام إدارة سيارات الحمل</p>
                        </div>
                        <a href="index.php?page=company_settings" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog me-1"></i> تعديل معلومات الشركة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي العمليات</h6>
                            <h2 class="card-text"><?php echo number_format($stats['operations']); ?></h2>
                        </div>
                        <i class="fas fa-truck-moving fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">عمليات اليوم</h6>
                            <h2 class="card-text"><?php echo number_format($stats['today_operations']); ?></h2>
                        </div>
                        <i class="fas fa-calendar-day fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">عدد السائقين</h6>
                            <h2 class="card-text"><?php echo number_format($stats['drivers']); ?></h2>
                        </div>
                        <i class="fas fa-id-card fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي التكاليف</h6>
                            <h2 class="card-text"><?php echo number_format($stats['total_cost']); ?></h2>
                        </div>
                        <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم المواقع المرتبطة بالشركة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title">
                        <i class="material-icons me-2" style="vertical-align: middle;">domain</i>
                        المواقع المرتبطة بالشركة
                    </h5>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-primary"><?php echo count($camps); ?> موقع</span>
                        <?php if (hasPermission('manage_camps')): ?>
                            <a href="index.php?page=camps" class="btn btn-sm btn-outline-primary">
                                <i class="material-icons me-1" style="font-size: 16px;">add</i>
                                إضافة موقع
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($camps)): ?>
                        <div class="empty-state text-center py-5">
                            <i class="material-icons text-muted" style="font-size: 64px;">location_city</i>
                            <h6 class="text-muted mt-3 mb-2">لا توجد مواقع مسجلة</h6>
                            <p class="text-muted mb-3">ابدأ بإضافة المواقع لإدارة العمليات بشكل أفضل</p>
                            <?php if (hasPermission('manage_camps')): ?>
                                <a href="index.php?page=camps" class="btn btn-primary">
                                    <i class="material-icons me-1" style="font-size: 16px;">add_location</i>
                                    إضافة أول موقع
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($camps as $camp): ?>
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                    <div class="camp-card">
                                        <div class="camp-card-header">
                                            <div class="camp-status">
                                                <span class="status-indicator status-<?php echo $camp['status']; ?>"></span>
                                                <span class="status-text"><?php echo $camp['status'] === 'active' ? 'نشط' : 'غير نشط'; ?></span>
                                            </div>
                                            <div class="camp-actions">
                                                <?php if (hasPermission('manage_camps')): ?>
                                                    <a href="index.php?page=camps&action=edit&id=<?php echo $camp['id']; ?>" class="btn-icon" title="تعديل">
                                                        <i class="material-icons">edit</i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="camp-card-body">
                                            <div class="camp-icon-wrapper">
                                                <i class="material-icons camp-main-icon">location_on</i>
                                            </div>
                                            <h6 class="camp-name"><?php echo htmlspecialchars($camp['name']); ?></h6>
                                            <p class="camp-company">
                                                <i class="material-icons">business</i>
                                                <?php echo htmlspecialchars($camp['company_name']); ?>
                                            </p>
                                            <?php if (!empty($camp['location'])): ?>
                                                <p class="camp-location">
                                                    <i class="material-icons">place</i>
                                                    <?php echo htmlspecialchars($camp['location']); ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="camp-card-footer">
                                            <div class="camp-stats">
                                                <div class="stat-item">
                                                    <span class="stat-number"><?php echo number_format($camp['operations_count']); ?></span>
                                                    <span class="stat-label">عملية</span>
                                                </div>
                                                <div class="stat-divider"></div>
                                                <div class="stat-item">
                                                    <span class="stat-number"><?php echo number_format($camp['total_cost'] ?? 0); ?></span>
                                                    <span class="stat-label">د.ع</span>
                                                </div>
                                            </div>
                                            <a href="index.php?page=operations&camp_id=<?php echo $camp['id']; ?>" class="btn btn-sm btn-outline-primary w-100 mt-2">
                                                <i class="material-icons me-1" style="font-size: 16px;">visibility</i>
                                                عرض العمليات
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title">العمليات خلال الأسبوع الماضي</h5>
                </div>
                <div class="card-body">
                    <canvas id="operationsChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title">توزيع المواد</h5>
                </div>
                <div class="card-body">
                    <canvas id="materialsChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر العمليات -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title">آخر العمليات</h5>
                    <a href="index.php?page=operations" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>السائق</th>
                                    <th>المادة</th>
                                    <th>نوع العملية</th>
                                    <th>التكلفة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentOperations)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد عمليات حتى الآن</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recentOperations as $operation): ?>
                                        <tr>
                                            <td><?php echo $operation['id']; ?></td>
                                            <td><?php echo htmlspecialchars($operation['driver_name']); ?></td>
                                            <td><?php echo htmlspecialchars($operation['material_name']); ?></td>
                                            <td>
                                                <?php if ($operation['operation_type'] === 'entry'): ?>
                                                    <span class="badge bg-success">دخول</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">خروج</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo number_format($operation['cost']); ?> د.ع</td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($operation['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- سكريبت الرسوم البيانية -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // بيانات الرسم البياني للعمليات
    const operationsData = <?php echo $chartDataJson; ?>;

    // إنشاء الرسم البياني للعمليات
    const operationsCtx = document.getElementById('operationsChart').getContext('2d');
    new Chart(operationsCtx, {
        type: 'line',
        data: {
            labels: operationsData.labels,
            datasets: [{
                    label: 'عمليات الدخول',
                    data: operationsData.entry,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'عمليات الخروج',
                    data: operationsData.exit,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // بيانات الرسم البياني للمواد
    const materialsData = <?php echo $materialsDataJson; ?>;

    // إنشاء الرسم البياني للمواد
    const materialsCtx = document.getElementById('materialsChart').getContext('2d');
    new Chart(materialsCtx, {
        type: 'doughnut',
        data: {
            labels: materialsData.labels,
            datasets: [{
                data: materialsData.data,
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b'
                ],
                hoverBackgroundColor: [
                    '#2e59d9',
                    '#17a673',
                    '#2c9faf',
                    '#dda20a',
                    '#be2617'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '70%'
        }
    });
</script>