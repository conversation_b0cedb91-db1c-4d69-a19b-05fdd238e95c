<?php
// Basic user profile page
// You can expand this with actual user data and editing features

// Example: Fetch user info from session or database
$userInfo = isset($userInfo) ? $userInfo : [
    'name' => 'User Name',
    'email' => '<EMAIL>',
    'role' => 'User'
];
?>
<div class="container mt-4">
    <h2><i class="fas fa-user"></i> الملف الشخصي</h2>
    <div class="card mt-3">
        <div class="card-body">
            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($userInfo['name']); ?></p>
            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($userInfo['email']); ?></p>
            <p><strong>الدور:</strong> <?php echo htmlspecialchars($userInfo['role']); ?></p>
        </div>
    </div>
</div>